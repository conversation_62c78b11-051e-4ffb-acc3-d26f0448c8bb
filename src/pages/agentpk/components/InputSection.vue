<template>
  <div class="input-section">
    <!-- 标题 -->
    <h1 class="title">董会答：懂你、懂美团的问答助手</h1>
    <div class="underline"></div>
    
    <!-- 输入框区域 -->
    <div class="input-container">
      <input 
        v-model="inputText"
        type="text" 
        class="input-field"
        placeholder="请输入您的问题"
        @keyup.enter="handleSubmit"
      />
      <div class="button-group">
        <button class="submit-btn" @click="handleSubmit">
          提交
        </button>
        <button class="batch-test-btn" @click="handleBatchTest">
          批量测评
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const inputText = ref('');

const handleSubmit = () => {
  if (inputText.value.trim()) {
    console.log('提交问题:', inputText.value);
    // TODO: 处理提交逻辑
  }
};

const handleBatchTest = () => {
  console.log('批量测评');
  // TODO: 处理批量测评逻辑
};
</script>

<style lang="scss" scoped>
.input-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1300px;
  margin: 0 auto;
}

.title {
  font-size: 40px;
  font-weight: bold;
  color: #333;
  margin: 0 0 16px 0;
  text-align: center;
}

.underline {
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #8B7ED8 0%, #B794F6 100%);
  border-radius: 2px;
  margin-bottom: 40px;
}

.input-container {
  width: 100%;
  max-width: 1300px;
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 16px 28px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 50px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #8B7ED8;
    box-shadow: 0 4px 12px rgba(139, 126, 216, 0.2);
  }
}

.input-field {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 18px;
  color: #333;
  padding: 8px 0;

  &::placeholder {
    color: #999;
  }
}

.button-group {
  display: flex;
  gap: 12px;
}

.submit-btn,
.batch-test-btn {
  padding: 10px 24px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.submit-btn {
  background: linear-gradient(90deg, #8B7ED8 0%, #B794F6 100%);
  color: white;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 126, 216, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.batch-test-btn {
  background: linear-gradient(90deg, #A78BFA 0%, #C084FC 100%);
  color: white;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(167, 139, 250, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .title {
    font-size: 28px;
  }

  .input-container {
    flex-direction: column;
    gap: 12px;
    padding: 16px 20px;
    border-radius: 20px;
  }

  .input-field {
    text-align: center;
    font-size: 16px;
  }

  .button-group {
    width: 100%;
    justify-content: center;
  }

  .submit-btn,
  .batch-test-btn {
    flex: 1;
    max-width: 120px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .title {
    font-size: 24px;
  }

  .input-container {
    margin: 0 20px;
  }
}
</style>
