<template>
  <div class="agentpk-fullscreen">
    <!-- 顶部返回按钮 -->
    <div class="top-bar">
      <button class="back-button" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回
      </button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <InputSection />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { onMounted, onUnmounted } from 'vue';
import InputSection from './components/InputSection.vue';

const router = useRouter();

// 返回首页
const goBack = async () => {
  console.log('🔙 [agentpk] 返回首页');
  await router.push({
    name: 'chat', // 返回到index.vue (chat路由)
  });
};

// 设置全屏样式
const setFullscreenStyles = () => {
  // 添加全屏样式类
  document.documentElement.classList.add('agentpk-active');
  document.body.classList.add('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.add('agentpk-active');
  }

  console.log('🎨 [agentpk] 已应用全屏样式');
};

// 移除全屏样式
const removeFullscreenStyles = () => {
  document.documentElement.classList.remove('agentpk-active');
  document.body.classList.remove('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.remove('agentpk-active');
  }

  console.log('🎨 [agentpk] 已移除全屏样式');
};

onMounted(() => {
  console.log('🚀 [agentpk] 全屏页面加载完成');
  setFullscreenStyles();
});

onUnmounted(() => {
  console.log('🔚 [agentpk] 页面卸载，恢复原始样式');
  removeFullscreenStyles();
});
</script>

<style lang="scss" scoped>
.agentpk-fullscreen {
  // 占满整个浏览器窗口，不受H5布局限制
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #faf9ff 50%, #f8f6ff 100%);
  z-index: 9999;
  overflow: auto;

  // 确保不受父容器样式影响
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.top-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(139, 126, 216, 0.1);
  border: 1px solid rgba(139, 126, 216, 0.3);
  border-radius: 8px;
  color: #333;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(139, 126, 216, 0.2);
    border-color: rgba(139, 126, 216, 0.5);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  svg {
    width: 18px;
    height: 18px;
  }
}

.main-content {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 100%;
  max-width: 1200px;
  padding: 0 40px;
}



// 响应式设计
@media (max-width: 768px) {
  .top-bar {
    height: 50px;
    padding: 0 15px;
  }

  .back-button {
    padding: 6px 12px;
    font-size: 13px;

    svg {
      width: 16px;
      height: 16px;
    }
  }

  .main-content {
    padding: 0 15px;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 0 10px;
  }
}
</style>

<style>
/* 全局样式，确保页面真正占满全屏 */
body.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

html.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

#app.agentpk-active {
  overflow: hidden !important;
}
</style>
